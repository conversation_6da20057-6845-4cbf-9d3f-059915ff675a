#!/usr/bin/env python3
"""
Optimized Streamlit Application for Contract Analysis using Azure AI Agent
"""

import streamlit as st
import os
import json
import time
from pathlib import Path
import pandas as pd

# Import existing modules - reuse existing functionality
from utils_fr_PolicyReview import _extract_data_from_file, _initialize_AIAgentProject_client
from framework_validator import validate_uploaded_guidelines_file
import json

# Page configuration
st.set_page_config(
    page_title="Contract Analysis AI Agent",
    page_icon="📄",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state
if 'analysis_in_progress' not in st.session_state:
    st.session_state.analysis_in_progress = False
if 'analysis_complete' not in st.session_state:
    st.session_state.analysis_complete = False
if 'analysis_results' not in st.session_state:
    st.session_state.analysis_results = None

# Custom CSS (simplified)
st.markdown("""
<style>
    .main-header { font-size: 2.5rem; color: #1f77b4; text-align: center; margin-bottom: 2rem; }
    .success-message { background-color: #d4edda; padding: 1rem; border-radius: 5px; border-left: 5px solid #28a745; }
    .warning-message { background-color: #fff3cd; padding: 1rem; border-radius: 5px; border-left: 5px solid #ffc107; }
</style>
""", unsafe_allow_html=True)

# Utility functions (simplified and reused)
def save_uploaded_file(uploaded_file, directory="temp_uploads"):
    """Save uploaded file to temporary directory"""
    os.makedirs(directory, exist_ok=True)
    file_path = os.path.join(directory, uploaded_file.name)
    with open(file_path, "wb") as f:
        f.write(uploaded_file.getbuffer())
    return file_path

def validate_custom_framework(guidelines_file):
    """Validate custom framework file"""
    if not guidelines_file:
        return False, []
    
    try:
        file_content = guidelines_file.read().decode('utf-8')
        guidelines_file.seek(0)  # Reset file pointer
        is_valid, errors, _ = validate_uploaded_guidelines_file(file_content)
        return is_valid, errors
    except Exception as e:
        return False, [f"Error validating framework: {str(e)}"]

def get_or_create_agent():
    """Get or create the Azure AI Agent for contract analysis"""
    if 'agent_client' not in st.session_state:
        # Initialize the agent client and create agent
        from azure.ai.projects.models import FunctionTool, ToolSet
        from ToolsetForPolicyReview import (analyze_contract_for_obligations,
                                           extract_fields_using_template,
                                           analyze_document_and_extract_tables)

        # Initialize project client
        project_client, model_deployment_name = _initialize_AIAgentProject_client()

        # Create agent with tools
        all_functions_tool = FunctionTool(functions=[analyze_contract_for_obligations, extract_fields_using_template, analyze_document_and_extract_tables])

        toolset = ToolSet()
        toolset.add(all_functions_tool)

        agent_instructions = """
        You are an expert legal contract analysis assistant specialized in the EYGS SRM Function D&O Framework. You follow a strict three-step process for comprehensive contract analysis.

        TOOL USAGE AND PARAMETERS:
        - `extract_fields_using_template`: Use this tool first. It requires the 'contract_text' and 'template_query'. Extract these values from the user's prompt. To save the output, ensure you call the tool with the `save_to_file=True` parameter.
        - `analyze_contract_for_obligations`: Use this tool second for advanced obligation extraction. It requires the 'contract_text' and 'guidelines_path'. Extract these values from the user's prompt. To save the output, ensure you call the tool with the `save_to_file=True` parameter.
        - `analyze_document_and_extract_tables`: Use this tool last. It requires the 'pdf_path'. Extract this value from the user's prompt. To save the output, ensure you call it with `save_to_excel=True` and `save_to_json=True`.

        EXECUTION STRATEGY:
        1. First, call `extract_fields_using_template` with the contract_text and template_query from user input to extract contract metadata and key fields.
        2. Second, call `analyze_contract_for_obligations` with the contract_text and guidelines_path from user input to perform comprehensive obligation extraction.
        3. Third, call `analyze_document_and_extract_tables` with the pdf_path from user input to extract all tables and structured data.

        IMPORTANT: Your final response to the user should be a summary message confirming that all three analyses were completed using the advanced framework and the files were saved.
        """

        agent = project_client.agents.create_agent(
            model=model_deployment_name,
            name="streamlit-contract-analysis-agent",
            instructions=agent_instructions,
            toolset=toolset,
        )

        thread = project_client.agents.create_thread()

        # Store in session state
        st.session_state.agent_client = project_client
        st.session_state.agent = agent
        st.session_state.thread = thread

    return st.session_state.agent_client, st.session_state.agent, st.session_state.thread

def run_analysis_pipeline(contract_path, template_name, guidelines_path):
    """Run the complete contract analysis pipeline using Azure AI Agent"""
    progress_bar = st.progress(0)
    status_text = st.empty()

    try:
        # Step 1: Extract contract text
        status_text.text("Extracting contract text...")
        progress_bar.progress(20)
        contract_text = _extract_data_from_file(contract_path)
        if not contract_text or len(contract_text.strip()) < 100:
            raise ValueError("Contract text is too short or empty")

        # Step 2: Get or create the Azure AI Agent
        status_text.text("Initializing AI Agent...")
        progress_bar.progress(40)
        project_client, agent, thread = get_or_create_agent()

        # Step 3: Prepare the agentic message
        status_text.text("Sending analysis request to AI Agent...")
        progress_bar.progress(60)

        user_message = f"""
        Please perform a comprehensive analysis on the provided contract.

        INPUTS:
        - PDF Path for table extraction: {contract_path}
        - Guidelines Path for obligation analysis: {guidelines_path}
        - Contract Type for template extraction: {template_name}
        - Full Contract Text: {contract_text}

        Please execute all three analysis steps and return the results.
        """

        # Send message to agent
        project_client.agents.create_message(
            thread_id=thread.id,
            role="user",
            content=user_message,
        )

        # Step 4: Run the agent and wait for completion
        status_text.text("AI Agent is analyzing the contract...")
        progress_bar.progress(80)

        run = project_client.agents.create_run(thread_id=thread.id, assistant_id=agent.id)

        # Poll for completion
        while run.status in ["queued", "in_progress", "requires_action"]:
            time.sleep(2)
            run = project_client.agents.get_run(thread_id=thread.id, run_id=run.id)

            if run.status == "requires_action":
                # Handle tool calls if needed
                tool_calls = run.required_action.submit_tool_outputs.tool_calls

                for tool_call in tool_calls:
                    # The agent will handle tool execution automatically
                    # This is just for monitoring
                    pass

        # Step 5: Get the agent's response
        status_text.text("Retrieving analysis results...")
        progress_bar.progress(90)

        messages = project_client.agents.list_messages(thread_id=thread.id)
        latest_message = messages.data[0]  # Get the most recent message

        progress_bar.progress(100)
        status_text.text("Analysis completed successfully!")

        # Return the agent's response
        return {
            "agent_response": latest_message.content[0].text.value,
            "status": "completed",
            "run_id": run.id
        }

    except Exception as e:
        st.error(f"Error in analysis: {e}")
        return None

def display_agent_results(results):
    """Display results from the Azure AI Agent"""
    if 'agent_response' in results:
        st.subheader("🤖 AI Agent Analysis Summary")

        # Display the agent's response
        agent_response = results['agent_response']
        st.markdown(agent_response)

        # Show run details
        if 'run_id' in results:
            with st.expander("🔍 Analysis Details"):
                st.write(f"**Run ID:** {results['run_id']}")
                st.write(f"**Status:** {results['status']}")

        # Note about file outputs
        st.info("""
        📁 **Output Files Generated:**
        - Template extraction results saved to JSON
        - Obligations analysis saved to JSON
        - Table extraction saved to Excel and JSON (for PDF files)

        The AI Agent has automatically executed all requested analysis steps and saved the detailed results to files.
        """)
    else:
        st.error("No valid results received from the AI Agent")

# Main application
def main():
    st.markdown('<h1 class="main-header">📄 Contract Analysis AI Agent</h1>', unsafe_allow_html=True)
    
    # Sidebar for inputs
    with st.sidebar:
        st.header("📁 Upload Files")
        
        # Contract file upload
        st.subheader("1. Contract File")
        contract_file = st.file_uploader(
            "Upload contract file",
            type=['pdf', 'txt'],
            help="Upload your contract in PDF or TXT format"
        )
        
        # Template type input
        st.subheader("2. Contract Template Type")
        template_name = st.text_input(
            "Enter template type",
            placeholder="e.g., MSA, Statement of Work, NDA",
            help="Specify the type of contract template for field extraction"
        )
        
        # Guidelines framework selection (simplified)
        st.subheader("3. Guidelines Framework")
        use_default_guidelines = st.checkbox(
            "Use Advanced EYGS SRM D&O Framework (Recommended)", 
            value=True,
            help="Uses the comprehensive Advanced_guidelines.json framework"
        )
        
        guidelines_file = None
        if not use_default_guidelines:
            guidelines_file = st.file_uploader(
                "Upload custom guidelines JSON file",
                type=['json'],
                help="Must match Advanced_guidelines.json structure"
            )
            
            if guidelines_file:
                is_valid, _ = validate_custom_framework(guidelines_file)
                if is_valid:
                    st.success("✅ Valid custom framework")
                else:
                    st.error("❌ Invalid framework - will use default")
                    guidelines_file = None
        
        if use_default_guidelines or guidelines_file is None:
            st.success("✅ Using Advanced EYGS SRM D&O Framework")
    
    # Main content area
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.header("🚀 Analysis Control")
        
        # Check inputs
        all_inputs_ready = (
            contract_file is not None and 
            template_name.strip() and 
            (use_default_guidelines or guidelines_file is not None)
        )
        
        if not all_inputs_ready:
            missing = []
            if not contract_file: missing.append("Contract file")
            if not template_name.strip(): missing.append("Template type")
            if not use_default_guidelines and not guidelines_file: missing.append("Guidelines framework")
            st.warning(f"Please provide: {', '.join(missing)}")
        
        # Analysis button
        if st.button("🔍 Start Analysis", disabled=not all_inputs_ready or st.session_state.analysis_in_progress):
            if all_inputs_ready:
                st.session_state.analysis_in_progress = True
                st.session_state.analysis_complete = False
                
                # Save files and run analysis
                contract_path = save_uploaded_file(contract_file)
                
                if use_default_guidelines:
                    guidelines_path = "Advanced_guidelines.json"
                else:
                    guidelines_path = save_uploaded_file(guidelines_file)
                
                # Run analysis
                with st.spinner("Running contract analysis..."):
                    results = run_analysis_pipeline(contract_path, template_name.strip(), guidelines_path)
                
                if results:
                    st.session_state.analysis_results = results
                    st.session_state.analysis_complete = True
                    st.success("🎉 Analysis completed successfully!")
                
                st.session_state.analysis_in_progress = False
    
    with col2:
        st.header("📋 Status")
        if contract_file:
            file_ext = Path(contract_file.name).suffix.lower()
            if file_ext == '.pdf':
                st.info("📄 PDF - Full analysis including tables")
            else:
                st.warning("📝 Text - Table extraction skipped")
        
        if st.session_state.analysis_in_progress:
            st.info("⏳ Analysis in progress...")
    
    # Results section
    if st.session_state.analysis_complete and st.session_state.analysis_results:
        st.markdown("---")
        st.header("📊 Analysis Results")

        results = st.session_state.analysis_results
        display_agent_results(results)

if __name__ == "__main__":
    main()
